<?php

declare(strict_types=1);

namespace Pressbear\Controllers\Auth;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Fortify\Contracts\LogoutResponse as LogoutResponseContract;
use Symfony\Component\HttpFoundation\Response;
use Pressbear\Events\BrowserLoggedOut;
use Illuminate\Support\Facades\Log;

class LogoutResponse implements LogoutResponseContract
{
    /**
     * Create an HTTP response that represents the object.
     *
     * @param Request $request
     * @return Response
     */
    public function toResponse($request)
    {
        // Logout User Current
        $sessionId = $request->session()->getId();
        event(new BrowserLoggedOut($sessionId));

        return $request->wantsJson()
            ? new JsonResponse('', 204)
            : redirect('/');
    }
}
