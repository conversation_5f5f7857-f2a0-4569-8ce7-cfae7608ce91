<?php

namespace Pressbear\Controllers\Auth;

use Lara<PERSON>\Fortify\Contracts\LoginResponse as LoginResponseContract;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;

class LoginResponse implements LoginResponseContract
{
    /**
     * Create an HTTP response that represents the object.
     *
     * @param Request $request
     * @return Response
     */
    public function toResponse($request)
    {
        /**
         * @var \App\Models\User $user
         */

        $user = Auth::user();
        $user->update([
            'last_login_ip' => $request->ip()
        ]);

        // Redirect based on user role
        return match ($user->role) {
            'superadmin' => redirect()->route('admin.dashboard'),
            'admin' => redirect()->route('admin.dashboard'),
            'sales' => redirect()->route('admin.dashboard'),
            'finance' => redirect()->route('admin.dashboard'),
            'outreach' => redirect()->route('admin.dashboard'),
            'publisher' => redirect()->route('publisher.dashboard'),
            'advertiser' => redirect()->route('marketplace'),
            'writer' => redirect()->route('admin.writer.dashboard'),
            default => redirect()->route('marketplace'),
        };
    }
}
