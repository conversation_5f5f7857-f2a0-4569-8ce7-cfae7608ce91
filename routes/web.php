<?php

// \Debugbar::disable();

use App\Http\Controllers\TestController;
use Illuminate\Support\Facades\Route;
use App\Livewire\Pages\PaymentSuccessPage;

use App\Http\Controllers\WebHookController;
use App\Http\Controllers\Modules\UploadController;
use App\Http\Controllers\MarketplaceCartController;
use App\Http\Controllers\Modules\MessageController;
use App\Http\Controllers\PaymentSettingsController;
use App\Http\Controllers\MarketplacePaymentController;
use App\Http\Controllers\MarketplaceWebsiteController;
use App\Http\Controllers\Modules\LocalizationController;
use App\Http\Controllers\Modules\NotificationController;
use App\Http\Controllers\Advertiser\AdvertiserController;
use App\Http\Controllers\MarketplaceSingleOrderItemController;



/**********************************************************************************************************/
//---------------------------------------  MARKETPLACE ROUTES  -------------------------------------------//
/**********************************************************************************************************/
Route::middleware(['auth', config('jetstream.auth_session'), 'verified'])
    ->group(function (): void {


        //--------------------------------------------------------------------------------//
        //                                    MARKETPLACE Ui                              //
        //--------------------------------------------------------------------------------//
        Route::get('/marketplace', [MarketplaceWebsiteController::class, 'marketHome'])
            ->name('marketplace');

        Route::get('/cart', [MarketplaceCartController::class, 'cartPage'])
            ->name('cart');

        Route::get('/cart/address', [MarketplacePaymentController::class, 'addressPage'])
            ->name('address-collect');

        Route::get('/payment', [MarketplacePaymentController::class, 'paymentPage'])
            ->name('payment-collect');

        Route::get('/payment/success', PaymentSuccessPage::class)
            ->name('payment-success'); //change address



        //--------------------------------------------------------------------------------//
        //                               MARKETPLACE FRAGMENTS                            //
        //--------------------------------------------------------------------------------//
        Route::get('/market/fragments', [MarketplaceWebsiteController::class, 'marketFragments'])
            ->name('fragment-cartPreview');

        Route::post('/market/fragments/table', [MarketplaceWebsiteController::class, 'marketTable'])
            ->name('market-table');



        //--------------------------------------------------------------------------------//
        //                        MARKETPLACE API BASED FUNCTIONALITY                    //
        //--------------------------------------------------------------------------------//
        Route::post('marketplace/cart/add', [MarketplaceCartController::class, 'add'])
            ->name('add-to-cart');

        Route::post('marketplace/cart/update', [MarketplaceCartController::class, 'update'])
            ->name('update-cart');

        Route::post('marketplace/payment/data', [MarketplacePaymentController::class, 'dataProcess'])
            ->name('data-process'); //address

        Route::any('/marketplace/process', [MarketplacePaymentController::class, 'processPayment'])
            ->name('payment-processing');

        Route::post('/order/item/update', [MarketplaceSingleOrderItemController::class, 'updateOrderItem'])
            ->name('order-update');

        Route::post('/upload', [UploadController::class, 'upload'])
            ->name('upload'); // File upload endpoint



        //--------------------------------------------------------------------------------//
        //                                   PAYMENT ROUTES                               //
        //--------------------------------------------------------------------------------//
        Route::get('/wallet/withdraw', [AdvertiserController::class, 'withdraw'])
            ->name('wallet.withdraw');

        Route::post('/wallet/withdraw', [AdvertiserController::class, 'withdrawStore'])
            ->name('wallet.withdraw.store');

        Route::get('/payment-settings', [PaymentSettingsController::class, 'index'])
            ->name('payment-settings.index');

        Route::post('/payment-settings', [PaymentSettingsController::class, 'store'])
            ->name('payment-settings.store');

        Route::post('/payment-settings/{id}/default', [PaymentSettingsController::class, 'setDefault'])
            ->name('payment-settings.set-default');

        Route::delete('/payment-settings/{id}', [PaymentSettingsController::class, 'destroy'])
            ->name('payment-settings.destroy');



        //--------------------------------------------------------------------------------//
        //                                   NOTIFICATIONS                                //
        //--------------------------------------------------------------------------------//
        Route::post('/notifications/{id}/mark-as-read', [NotificationController::class, 'markAsRead'])
            ->name('notifications.markAsRead');
        Route::post('/notifications/mark-all-as-read', [NotificationController::class, 'markAllAsRead'])
            ->name('notifications.markAllAsRead');



        //--------------------------------------------------------------------------------//
        //                                CHAT / MESSAGING                                //
        //--------------------------------------------------------------------------------//
        Route::get('/messages/{orderItemId}', [MessageController::class, 'index'])->name('messages.index');
        Route::post('/messages', [MessageController::class, 'store'])->name('messages.store');
        Route::post('/messages/{orderItemId}/mark-as-read', [MessageController::class, 'markAsRead'])
            ->name('messages.mark-as-read');
    });





/**********************************************************************************************************/
// ------------------------------------  FUNCTIONALITY ROUTES  ------------------------------------------ //
/**********************************************************************************************************/
Route::post('/webhooks/stripe', [WebHookController::class, 'stripe'])->name('webhook.stripe');
Route::get('language/{locale}', [LocalizationController::class, 'switch'])->name('language.switch');


//test route
Route::get('/test', [TestController::class, 'test']);




/**********************************************************************************************************/
// ------------------------------------------  PUBLIC PAGES   ------------------------------------------- //
/**********************************************************************************************************/
Route::get('/', fn() => view('public.home-marketplace'))->name('home');
Route::get('/about', fn() => view('public.about'))->name('about-us');
Route::get('/contact', fn() => view('public.contact'))->name('contact');




// Design examples
Route::get('/design',   fn() => view('public.example'));
Route::get('/design2',   fn() => view('public.example-two'));

// under dev
// Route::get('/blog',     fn()=>view('public.blog'))->name('blog');
// Route::get('/blog/post',fn()=>view('public.single-post'))->name('blog-post');
// Route::get('/features', fn()=>view('public.features'))->name('features');
