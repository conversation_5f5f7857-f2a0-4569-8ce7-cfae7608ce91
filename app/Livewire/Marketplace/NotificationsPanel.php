<?php

namespace App\Livewire\Marketplace;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class NotificationsPanel extends Component
{
    public $notifications;
    public $unreadCount;

    public function getListeners()
    {
        return [
            'notification-marked-read' => '$refresh',
        ];
    }

    public function mount()
    {
        $this->loadNotifications();
    }

    public function loadNotifications()
    {
        $this->notifications = Auth::user()->notifications()->latest()->get();
        $this->unreadCount = $this->notifications->where('read_at', null)->count();
        $this->dispatch('update-bell-count', count: $this->unreadCount);
    }

    public function markAsRead($id)
    {
        Auth::user()->notifications()->findOrFail($id)->markAsRead();
        $this->loadNotifications();
        $this->dispatch('notification-marked-read');
    }

    public function markAllAsRead()
    {
        Auth::user()->unreadNotifications->markAsRead();
        $this->loadNotifications();
        $this->dispatch('notification-marked-read');
        $this->dispatch('update-bell-count', count: $this->unreadCount);
    }

    public function render()
    {
        return view('livewire.marketplace.notifications-panel');
    }
}
