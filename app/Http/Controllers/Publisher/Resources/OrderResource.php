<?php

namespace App\Http\Controllers\Publisher\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;

class OrderResource extends JsonResource
{
    public function toArray($request)
    {
        $topics = $this->orderItems->first()?->website->topics?->pluck('name')->implode(', ') ?? 'N/A';

        return [
            'id' => $this->id,
            'topic' => ($topics) ? $topics : 'N/A',
            'niche' => $this->orderItems->first()?->niche ?? 'general',
            'price_paid' => $this->price_paid,
            'status' => $this->status,
            'delivery_date' => $this->orderItems->first()?->estimated_publication_date_formatted,
            'items_count' => $this->orderItems->count(),
            'customer_name' => $this->user->name,
            'created_at' => Carbon::parse($this->created_at)->format('Y-m-d'),
        ];
    }
}
